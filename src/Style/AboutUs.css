@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cormorant:ital,wght@0,300..700;1,300..700&display=swap');

.aboutus{
    background-color: whitesmoke;
    height: 100%;
    width: 100%;
}

.about_content1{
   margin-top: 100px;
   background-color: rgb(252, 252, 252);

}
.about_content1 h1{

    font-weight: bolder;
    font-size: 50px;
    padding-top: 8%;
      color: rgb(250, 165, 165);
      text-align: center;
      padding-bottom:5%;
}
.about_content1 p{
     text-align:justify;
     font-size: 22px;
     line-height: 30px;
     padding:1%;
     font-family: Cormorant, serif;
     font-weight: bold;
     
}




.services_looking_for{
    width: 100%;
    min-height:120vh;
    margin-top: -2%;
    background-color: rgb(252, 252, 252);
    display: flex;
    
  }
  .services_looking_for .services_info{
    margin-top: 25%;
    margin-left: 4%;
    height: 100%;
    width: 45%;
    background-color: rgb(250, 251, 250);
    
    }
  .services_looking_for .services_info .rows{
    padding-top: 5px;
    padding-bottom:5px;
    display: grid;
    grid-template-columns: repeat(auto-fit,minmax(50px,1fr));
    grid-gap:5px;
    height: 20%;
  }
  .services_looking_for .services_info .rows .columns{
    width: 100%;
    height:100%;
    background-color: rgb(239, 239, 244);
    padding-top:3px;
    cursor: pointer;
  }
  .services_looking_for .services_info .rows .columns img{
     margin-left: 40px;
     width: 80px;
     height: 80px;
     border-radius: 20px;
  }
  .services_looking_for .services_info .rows .columns p{
    padding-top: 5px;
    padding-bottom:4px;
    text-align:center;
    text-transform: uppercase;
    font-size: 15px;
    font-weight: bold;
    
  }
  .services_images{
    margin-top: 15%;
    margin-left: 10%;
    height: 100%;
    width: 45%;
    opacity: 0.7;
   
}
  .services_images img{
    border-radius: 30px;
    margin-top: 0px;
    height:260px;
    width:220px ;
  }

.services{
    width: 100%;
    min-height:120vh;
    background-color: rgb(252, 252, 252);
   
}
.services h1{
    text-transform: uppercase;
    background-color: rgb(252, 252, 252);
    padding-top: 10%;
  font-weight: bold;
    color: rgb(250, 165, 165);
    text-align: center;
    padding-bottom: 3%;

   
}
.services .row{
    padding-top: 8px;
    padding-bottom:8px;
    display: grid;
    grid-template-columns: repeat(auto-fit,minmax(100px,1fr));
    grid-gap:30px;
    height: 100%;
    justify-content: space-around;
    
}
.services .row .service{
    background-color: rgb(239, 239, 244);
    padding:20px 20px;
    text-align:center;
    border-radius: 30px;

}

.services .row .service h2{
     font-size: 20px;
     padding-bottom: 10px;
     font-weight:700;
     color:rgba(162, 229, 247, 0.595);
     

}
.services .row .service p{
    padding-top:15px ;
    font-family: Inter, sans-serif;
    font-size: 18px;
   
}

.servicesPara{
    width: 100%;
    height:100%;
    background-color: rgb(252, 252, 252);
    padding-top:40px;
}
.servicesPara h1{
    padding-top:5%;
    font-weight:600;
    font-size: 40px;
    color: rgb(250, 165, 165);
    text-align: center;
    background-color:  rgb(252, 252, 252); ;
    
    
}
.servicesPara p{
    padding-top:3%;
    font-size: 15px;
    font-family:Inter, sans-serif;

}
span{
  display: inline;
  font-size: 5px;
}
.services_info  h4{
  color:rgb(250, 165, 165);
  font-size: 20px;
  padding:2%;
  margin-left: 5px;
}

