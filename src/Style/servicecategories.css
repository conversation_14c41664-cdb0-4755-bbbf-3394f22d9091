/* Service Categories Styles - Urban Company inspired */

.service-categories-section {
  padding: 80px 0;
  background: #f8fafc;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 42px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 16px 0;
  font-family: 'Kanit', sans-serif;
}

.section-description {
  font-size: 18px;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Categories Container */
.categories-container {
  display: grid;
  gap: 30px;
}

.categories-container.grid {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.categories-container.horizontal {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

/* Category Card */
.category-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.4s ease;
  cursor: pointer;
  border: 2px solid transparent;
  position: relative;
}

.category-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border-color: #6366f1;
}

/* Image Container */
.category-image-container {
  position: relative;
  width: 100%;
  height: 220px;
  overflow: hidden;
}

.category-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.category-card:hover .category-image {
  transform: scale(1.1);
}

.category-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.8), rgba(139, 92, 246, 0.8));
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.category-card:hover .category-overlay {
  opacity: 1;
}

.category-icon {
  font-size: 48px;
  animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Content */
.category-content {
  padding: 30px;
}

.category-name {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 12px 0;
  font-family: 'Kanit', sans-serif;
}

.category-description {
  color: #6b7280;
  font-size: 16px;
  line-height: 1.6;
  margin: 0 0 20px 0;
}

.category-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
}

.service-count,
.subcategory-count {
  font-size: 14px;
  color: #6366f1;
  font-weight: 600;
  background: #ede9fe;
  padding: 6px 12px;
  border-radius: 20px;
}

.category-action {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #6366f1;
  font-weight: 600;
  font-size: 16px;
}

.explore-text {
  transition: transform 0.3s ease;
}

.category-card:hover .explore-text {
  transform: translateX(4px);
}

.category-action svg {
  transition: transform 0.3s ease;
  stroke-width: 2.5;
}

.category-card:hover .category-action svg {
  transform: translateX(4px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .service-categories-section {
    padding: 60px 0;
  }
  
  .container {
    padding: 0 16px;
  }
  
  .section-header {
    margin-bottom: 40px;
  }
  
  .section-title {
    font-size: 32px;
  }
  
  .section-description {
    font-size: 16px;
  }
  
  .categories-container {
    gap: 20px;
  }
  
  .categories-container.grid,
  .categories-container.horizontal {
    grid-template-columns: 1fr;
  }
  
  .category-image-container {
    height: 180px;
  }
  
  .category-content {
    padding: 24px;
  }
  
  .category-name {
    font-size: 20px;
  }
  
  .category-description {
    font-size: 14px;
  }
  
  .category-stats {
    gap: 12px;
    flex-wrap: wrap;
  }
  
  .service-count,
  .subcategory-count {
    font-size: 12px;
    padding: 4px 10px;
  }
}

@media (max-width: 480px) {
  .service-categories-section {
    padding: 40px 0;
  }
  
  .section-title {
    font-size: 28px;
  }
  
  .category-image-container {
    height: 160px;
  }
  
  .category-content {
    padding: 20px;
  }
  
  .category-name {
    font-size: 18px;
  }
  
  .category-action {
    font-size: 14px;
  }
}

/* Loading state */
.category-card.loading {
  opacity: 0.7;
  pointer-events: none;
}

.category-card.loading .category-image {
  background: #f1f5f9;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Special effects */
.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent, rgba(99, 102, 241, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.category-card:hover::before {
  opacity: 1;
}
