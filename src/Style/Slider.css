.carousel-container {
    position: relative;
    top:-15px;
    width: 100%;
    margin: 15px auto;
    height: 100%;
    overflow: hidden;
    background-color: rgb(252, 252, 252);
  }
  .carousel-container h1{
    padding-top:10%;
    font-weight:600;
    font-size: 40px;
    color: rgb(250, 165, 165);
    text-align: center;
    background-color:  rgb(252, 252, 252); ;
    
    
  }

  .carousel {
    display: flex;
    width:100%;
    height: 100%;
  }
  
  .carousel-track {
    display: flex;
    transition: transform 0.3s ease-in-out;
  }
  
  .carousel-slide {
   
    height: 100%;
    min-width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 3%;
  }
  
  .carousel-slide img {
    padding: 0px;
    padding-top: 10px;
    width: 100%;
    max-height: 550px;
    object-fit: cover;
    object-position: center;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease;
  }

  .carousel-slide img:hover {
    transform: scale(1.02);
  }
  
  .carousel-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.5);
    border: none;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    padding: 10px;
    z-index: 1000;
  }
  
  .carousel-button.prev {
    left: 10px;
  }
  
  .carousel-button.next {
    right: 10px;
  }
  
  .carousel-button:hover {
    background-color: rgba(0, 0, 0, 0.8);
  }
  
  .carousel-indicators {
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 5px;
  }
  
  .indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(156, 151, 151, 0.5);
    border: none;
    cursor: pointer;
  }
  
  .indicator.active {
    background-color: white;
  }

  /* Responsive Design for Better Image Quality */
  @media (max-width: 768px) {
    .carousel-slide img {
      max-height: 400px;
    }

    .carousel-container h1 {
      font-size: 32px;
      padding-top: 5%;
    }
  }

  @media (max-width: 480px) {
    .carousel-slide img {
      max-height: 300px;
      border-radius: 8px;
    }

    .carousel-container h1 {
      font-size: 28px;
    }

    .carousel-button {
      font-size: 1.5rem;
      padding: 8px;
    }
  }

  /* High DPI Display Optimizations */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .carousel-slide img {
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
    }
  }
  