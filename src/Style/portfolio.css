/* Portfolio Page Styles - Sapphire Interior inspired */

.portfolio-page {
  min-height: 100vh;
  background: #ffffff;
}

/* Hero Section */
.portfolio-hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 120px 0 80px 0;
  text-align: center;
  color: white;
  position: relative;
  overflow: hidden;
}

.portfolio-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="hexagons" width="60" height="52" patternUnits="userSpaceOnUse"><polygon points="30,0 52,15 52,37 30,52 8,37 8,15" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23hexagons)"/></svg>');
  opacity: 0.3;
  z-index: 0;
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.hero-title {
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 16px 0;
  font-family: 'Kanit', sans-serif;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 20px;
  font-weight: 400;
  margin: 0;
  opacity: 0.9;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Call to Action Section */
.portfolio-cta {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  padding: 80px 0;
  color: white;
  text-align: center;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;
}

.cta-title {
  font-size: 36px;
  font-weight: 700;
  margin: 0 0 16px 0;
  font-family: 'Kanit', sans-serif;
}

.cta-description {
  font-size: 18px;
  margin: 0 0 40px 0;
  opacity: 0.9;
  line-height: 1.6;
}

.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-btn {
  display: inline-block;
  padding: 16px 32px;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  min-width: 200px;
  text-align: center;
}

.cta-btn.primary {
  background: white;
  color: #6366f1;
  border-color: white;
}

.cta-btn.primary:hover {
  background: transparent;
  color: white;
  border-color: white;
  transform: translateY(-2px);
}

.cta-btn.secondary {
  background: transparent;
  color: white;
  border-color: white;
}

.cta-btn.secondary:hover {
  background: white;
  color: #6366f1;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .portfolio-hero {
    padding: 100px 0 60px 0;
  }
  
  .hero-title {
    font-size: 36px;
  }
  
  .hero-subtitle {
    font-size: 18px;
  }
  
  .portfolio-cta {
    padding: 60px 0;
  }
  
  .cta-title {
    font-size: 28px;
  }
  
  .cta-description {
    font-size: 16px;
    margin-bottom: 30px;
  }
  
  .cta-buttons {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }
  
  .cta-btn {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .hero-content {
    padding: 0 16px;
  }
  
  .hero-title {
    font-size: 28px;
  }
  
  .hero-subtitle {
    font-size: 16px;
  }
  
  .cta-title {
    font-size: 24px;
  }
  
  .cta-description {
    font-size: 15px;
  }
  
  .container {
    padding: 0 16px;
  }
}

/* Animation for page load */
.portfolio-page {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scroll animations */
.hero-content {
  animation: slideUp 0.8s ease-out;
}

.cta-content {
  animation: slideUp 0.8s ease-out 0.2s both;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading states */
.portfolio-page.loading {
  opacity: 0.7;
}

/* Hover effects for better interactivity */
.cta-btn {
  position: relative;
  overflow: hidden;
}

.cta-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.cta-btn:hover::before {
  left: 100%;
}
