@import url('https://fonts.googleapis.com/css2?family=Kanit:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=SUSE:wght@100..800&display=swap');

 body{
    overflow-y: scroll; 
    scrollbar-width: none; 
    -ms-overflow-style: none
} 
.background1{
    width:100%;
    min-height:120vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.background1::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    z-index: 0;
}

.background1 .header-content {
    position: relative;
    z-index: 1;
}
.header-content{
    margin-top:10px;
    text-align: center;
    max-width: 900px;
}
.header-content h1{
    font-size: 65px;
    font-weight:600;
    line-height:60px;
    font-family: Kanit, sans-serif;
    color: whitesmoke;
    margin-bottom: 20px;
}

.hero-subtitle {
    font-size: 24px;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;
    font-weight: 400;
}

.hero-search-container {
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.explore-services-btn {
    display: inline-block;
    background: #6366f1;
    color: white;
    padding: 16px 32px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 18px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.explore-services-btn:hover {
    background: #5856eb;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

.background2{
    width:100%;
    min-height:120vh;
    background: #f8fafc;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 80px 0;
}

.services-preview {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.background3{
    width:100%;
    min-height:120vh;
    background: white;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 80px 0;
}

.popular-services-preview {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
}

.popular-services-preview .section-title {
    font-size: 36px;
    font-weight: 700;
    color: #111827;
    margin-bottom: 50px;
    font-family: 'Kanit', sans-serif;
}

.services-grid-preview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
}

.view-all-services {
    display: inline-block;
    background: transparent;
    color: #6366f1;
    padding: 16px 32px;
    border: 2px solid #6366f1;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 18px;
    transition: all 0.3s ease;
}

.view-all-services:hover {
    background: #6366f1;
    color: white;
    transform: translateY(-2px);
}

.background4{
    width:100%;
    min-height:120vh;
    background: #f8fafc;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 80px 0;
}

.portfolio-preview {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.portfolio-cta-home {
    text-align: center;
    margin-top: 50px;
}

.view-portfolio-btn {
    display: inline-block;
    background: #6366f1;
    color: white;
    padding: 16px 32px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 18px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.view-portfolio-btn:hover {
    background: #5856eb;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}


@keyframes moveBackground {
    0% {
        background-size: 95%;
      }
      100% {
        background-size: 100%;
      }
  }
.background1 h1{
  padding-top: 5%;
  font-weight: bold;
    color: white;
    text-align: center;
    padding-bottom: 3%;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content h1 {
    font-size: 42px;
    line-height: 48px;
  }

  .hero-subtitle {
    font-size: 20px;
    margin-bottom: 30px;
  }

  .hero-search-container {
    margin-bottom: 30px;
  }

  .explore-services-btn {
    padding: 14px 28px;
    font-size: 16px;
  }

  .popular-services-preview .section-title {
    font-size: 28px;
    margin-bottom: 40px;
  }

  .services-grid-preview {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
  }

  .view-all-services {
    padding: 14px 28px;
    font-size: 16px;
  }

  .view-portfolio-btn {
    padding: 14px 28px;
    font-size: 16px;
  }

  .portfolio-cta-home {
    margin-top: 40px;
  }
}

@media (max-width: 480px) {
  .header-content h1 {
    font-size: 32px;
    line-height: 38px;
  }

  .hero-subtitle {
    font-size: 18px;
  }

  .explore-services-btn {
    padding: 12px 24px;
    font-size: 15px;
  }

  .popular-services-preview .section-title {
    font-size: 24px;
  }

  .services-grid-preview {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .services-preview,
  .popular-services-preview,
  .portfolio-preview {
    padding: 0 16px;
  }

  .view-portfolio-btn {
    padding: 12px 24px;
    font-size: 15px;
  }
}


