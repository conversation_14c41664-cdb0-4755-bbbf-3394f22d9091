/* Portfolio Gallery Styles - Sapphire Interior inspired */

.portfolio-gallery {
  padding: 80px 0;
  background: #f8fafc;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.portfolio-header {
  text-align: center;
  margin-bottom: 60px;
}

.portfolio-title {
  font-size: 42px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 16px 0;
  font-family: 'Kanit', sans-serif;
}

.portfolio-subtitle {
  font-size: 18px;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Filters */
.portfolio-filters {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 50px;
}

.filter-btn {
  background: white;
  color: #6b7280;
  border: 2px solid #e5e7eb;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-btn:hover {
  border-color: #6366f1;
  color: #6366f1;
  transform: translateY(-2px);
}

.filter-btn.active {
  background: #6366f1;
  color: white;
  border-color: #6366f1;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

/* Portfolio Grid */
.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.portfolio-item {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.4s ease;
  cursor: pointer;
  position: relative;
}

.portfolio-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.portfolio-image-container {
  position: relative;
  width: 100%;
  height: 280px;
  overflow: hidden;
}

.portfolio-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.portfolio-item:hover .portfolio-image {
  transform: scale(1.1);
}

/* Overlay */
.portfolio-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.9), rgba(139, 92, 246, 0.9));
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.portfolio-item:hover .portfolio-overlay {
  opacity: 1;
}

.portfolio-content {
  text-align: center;
  color: white;
  padding: 20px;
}

.portfolio-item-title {
  font-size: 20px;
  font-weight: 700;
  margin: 0 0 8px 0;
  font-family: 'Kanit', sans-serif;
}

.portfolio-category {
  font-size: 14px;
  opacity: 0.9;
  margin: 0 0 16px 0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.view-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.portfolio-item:hover .view-icon {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.view-icon svg {
  stroke-width: 2;
}

/* No Items */
.no-items {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
  font-size: 18px;
}

/* Modal */
.portfolio-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background: white;
  border-radius: 16px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  animation: slideUp 0.3s ease;
}

.modal-close {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1001;
  transition: background 0.3s ease;
}

.modal-close:hover {
  background: rgba(0, 0, 0, 0.7);
}

.modal-image-container {
  width: 100%;
  max-height: 70vh;
  overflow: hidden;
}

.modal-image {
  width: 100%;
  height: auto;
  display: block;
}

.modal-info {
  padding: 24px;
}

.modal-title {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 8px 0;
  font-family: 'Kanit', sans-serif;
}

.modal-category {
  font-size: 14px;
  color: #6366f1;
  margin: 0 0 16px 0;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
}

.modal-description {
  font-size: 16px;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .portfolio-gallery {
    padding: 60px 0;
  }
  
  .portfolio-title {
    font-size: 32px;
  }
  
  .portfolio-subtitle {
    font-size: 16px;
  }
  
  .portfolio-filters {
    gap: 8px;
    margin-bottom: 40px;
  }
  
  .filter-btn {
    padding: 10px 20px;
    font-size: 13px;
  }
  
  .portfolio-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
  }
  
  .portfolio-image-container {
    height: 220px;
  }
  
  .portfolio-item-title {
    font-size: 18px;
  }
  
  .modal-content {
    max-width: 95vw;
    max-height: 95vh;
  }
  
  .modal-info {
    padding: 20px;
  }
  
  .modal-title {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .portfolio-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .portfolio-image-container {
    height: 200px;
  }
  
  .portfolio-filters {
    flex-direction: column;
    align-items: center;
  }
  
  .filter-btn {
    width: 200px;
    text-align: center;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
