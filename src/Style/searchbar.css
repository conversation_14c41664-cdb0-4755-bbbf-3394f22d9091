/* Search Bar Styles - Urban Company inspired */

.search-bar-container {
  position: relative;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.search-form {
  width: 100%;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.search-input-wrapper:focus-within {
  border-color: #6366f1;
  box-shadow: 0 4px 25px rgba(99, 102, 241, 0.15);
}

.search-input {
  flex: 1;
  padding: 16px 20px;
  border: none;
  outline: none;
  font-size: 16px;
  font-weight: 400;
  color: #374151;
  background: transparent;
  border-radius: 12px;
}

.search-input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.search-button {
  padding: 12px 16px;
  background: #6366f1;
  border: none;
  border-radius: 0 12px 12px 0;
  color: white;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-button:hover {
  background: #5856eb;
}

.search-button svg {
  width: 20px;
  height: 20px;
  stroke-width: 2;
}

/* Dropdown Styles */
.search-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  margin-top: 8px;
  z-index: 1000;
  max-height: 400px;
  overflow-y: auto;
}

.suggestions-header {
  padding: 12px 20px;
  border-bottom: 1px solid #f3f4f6;
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
  background: #f9fafb;
  border-radius: 12px 12px 0 0;
}

.suggestion-item {
  padding: 16px 20px;
  cursor: pointer;
  border-bottom: 1px solid #f3f4f6;
  transition: all 0.2s ease;
}

.suggestion-item:hover,
.suggestion-item.selected {
  background: #f8fafc;
  border-left: 4px solid #6366f1;
}

.suggestion-item:last-child {
  border-bottom: none;
  border-radius: 0 0 12px 12px;
}

.suggestion-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.suggestion-main {
  flex: 1;
}

.suggestion-name {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: #111827;
  margin-bottom: 4px;
}

.suggestion-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
}

.suggestion-rating {
  color: #f59e0b;
  font-weight: 500;
}

.suggestion-reviews {
  color: #6b7280;
}



.suggestion-duration {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-bar-container {
    max-width: 100%;
    margin: 0 16px;
  }
  
  .search-input {
    padding: 14px 16px;
    font-size: 15px;
  }
  
  .search-button {
    padding: 10px 14px;
  }
  
  .suggestion-item {
    padding: 14px 16px;
  }
  
  .suggestion-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .suggestion-meta {
    gap: 8px;
    font-size: 13px;
  }
  
  .suggestion-duration {
    font-size: 13px;
  }
}

/* Loading state */
.search-input-wrapper.loading {
  opacity: 0.7;
}

.search-input-wrapper.loading .search-button {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Focus states for accessibility */
.suggestion-item:focus {
  outline: 2px solid #6366f1;
  outline-offset: -2px;
}

.search-input:focus {
  outline: none;
}

/* Animation for dropdown */
.search-dropdown {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scrollbar styling for dropdown */
.search-dropdown::-webkit-scrollbar {
  width: 6px;
}

.search-dropdown::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.search-dropdown::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.search-dropdown::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
