/* Service Card Styles - Urban Company inspired */

.service-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #f1f5f9;
  height: fit-content;
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.service-card.clickable {
  cursor: pointer;
}

.service-card.clickable:hover {
  border-color: #6366f1;
}

/* Image Container */
.service-image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.service-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.service-card:hover .service-image {
  transform: scale(1.05);
}

/* Badges */
.service-badges {
  position: absolute;
  top: 12px;
  left: 12px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge.popular {
  background: #ef4444;
  color: white;
}

.badge.new {
  background: #10b981;
  color: white;
}

.badge.discount {
  background: #f59e0b;
  color: white;
}

/* Content */
.service-content {
  padding: 20px;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 12px;
}

.service-name {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
  line-height: 1.3;
  flex: 1;
}

.service-rating {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  white-space: nowrap;
}

.rating-star {
  font-size: 16px;
}

.rating-value {
  font-weight: 600;
  color: #111827;
}

.rating-count {
  color: #6b7280;
}

.service-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 16px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Meta Information */
.service-meta {
  margin-bottom: 16px;
}

.service-duration {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 14px;
}

.service-duration svg {
  width: 16px;
  height: 16px;
  stroke-width: 2;
}

/* Action */
.service-action {
  display: flex;
  justify-content: center;
  align-items: center;
}

.contact-button {
  background: #6366f1;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  width: 100%;
}

.contact-button:hover {
  background: #5856eb;
  transform: translateY(-1px);
}

/* Tags */
.service-tags {
  margin-top: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.service-tag {
  background: #f1f5f9;
  color: #475569;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

/* Variants */
.service-card.compact {
  border-radius: 12px;
}

.service-card.compact .service-image-container {
  height: 150px;
}

.service-card.compact .service-content {
  padding: 16px;
}

.service-card.compact .service-name {
  font-size: 16px;
}



.service-card.featured {
  border: 2px solid #6366f1;
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.15);
}

.service-card.featured .service-image-container {
  height: 240px;
}

.service-card.featured .service-content {
  padding: 24px;
}

.service-card.featured .service-name {
  font-size: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .service-card {
    border-radius: 12px;
  }
  
  .service-image-container {
    height: 180px;
  }
  
  .service-content {
    padding: 16px;
  }
  
  .service-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .service-name {
    font-size: 16px;
  }
  
  .service-action {
    margin-top: 16px;
  }

  .contact-button {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .service-image-container {
    height: 160px;
  }
  
  .service-content {
    padding: 14px;
  }
  
  .service-name {
    font-size: 15px;
  }
  
  .service-description {
    font-size: 13px;
  }
  

}

/* Loading state */
.service-card.loading {
  opacity: 0.7;
  pointer-events: none;
}

.service-card.loading .service-image {
  background: #f1f5f9;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
