.gallery{
   width: 100%;
  min-height: 90vh;
  padding: 2%;
  margin-top: 10px;
}
.image_gallery{
     padding-left: 4%;
     padding-right: 4%;
      margin-top:135px;
    width: 100%;
    background-color:rgb(255, 255, 255);
    display: grid;
    grid-template-columns: repeat(auto-fit,minmax(300px,1fr));
    grid-gap:20px;
    justify-content: space-around;
}
.image_gallery img{
    margin-top: 5px;
    margin-bottom: 1%;
    width: 100%;
    height: 250px;
    cursor: pointer;
    object-fit: cover;
    object-position: center;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    image-rendering: pixelated;
    border-radius: 8px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.image_gallery img:hover{
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}
.full-img{
    width: 100%;
    height: 100vh;
    background:rgba(0,0,0,0.9);
    position: fixed;
    top:0;
    left:0;
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}
.full-img img{
    width: 80%;
    max-width: 800px;
    max-height: 80vh;
    margin-top: 30px;
    object-fit: contain;
    object-position: center;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}
.full-img span{
    position: absolute;
    top: 20px;
    right: 30px;
    font-size: 30px;
    color: white;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.full-img span:hover{
    background: rgba(255, 0, 0, 0.7);
}

/* Responsive Design for Better Image Quality */
@media (max-width: 768px) {
    .image_gallery {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        padding-left: 2%;
        padding-right: 2%;
    }

    .image_gallery img {
        height: 200px;
    }

    .full-img img {
        width: 95%;
        max-width: 95vw;
    }

    .full-img span {
        top: 10px;
        right: 15px;
        font-size: 25px;
    }
}

@media (max-width: 480px) {
    .image_gallery {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        grid-gap: 15px;
    }

    .image_gallery img {
        height: 180px;
    }
}

/* High DPI Display Optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .image_gallery img,
    .full-img img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

