/* Services Page Styles - Urban Company inspired */

.services-page {
  min-height: 100vh;
  background: #ffffff;
}

/* Hero Section */
.services-hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 120px 0 80px 0;
  text-align: center;
  color: white;
  position: relative;
  overflow: hidden;
}

.services-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23dots)"/></svg>');
  opacity: 0.4;
  z-index: 0;
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.hero-title {
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 16px 0;
  font-family: 'Kanit', sans-serif;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 24px;
  font-weight: 400;
  margin: 0 0 40px 0;
  opacity: 0.9;
}

.hero-search {
  margin-bottom: 60px;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 60px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Section Headers */
.section-header {
  text-align: center;
  margin-bottom: 50px;
}

.section-title {
  font-size: 36px;
  font-weight: 700;
  color: #111827;
  margin: 0;
  font-family: 'Kanit', sans-serif;
}

/* Services Grid */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

/* Popular Services Section */
.popular-services {
  padding: 80px 0;
  background: #f8fafc;
}

/* New Services Section */
.new-services {
  padding: 80px 0;
  background: white;
}

/* Filtered Services Section */
.filtered-services {
  padding: 40px 0 80px 0;
  background: #f8fafc;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e5e7eb;
}

.filter-info {
  flex: 1;
}

.filter-title {
  font-size: 32px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 8px 0;
  font-family: 'Kanit', sans-serif;
}

.filter-count {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.clear-filters {
  background: #6366f1;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-filters:hover {
  background: #5856eb;
  transform: translateY(-1px);
}

.no-services {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
  font-size: 18px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .services-hero {
    padding: 100px 0 60px 0;
  }
  
  .hero-title {
    font-size: 36px;
  }
  
  .hero-subtitle {
    font-size: 20px;
    margin-bottom: 30px;
  }
  
  .hero-search {
    margin-bottom: 40px;
  }
  
  .hero-stats {
    gap: 40px;
  }
  
  .stat-number {
    font-size: 28px;
  }
  
  .section-title {
    font-size: 28px;
  }
  
  .services-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
  }
  
  .popular-services,
  .new-services {
    padding: 60px 0;
  }
  
  .filtered-services {
    padding: 30px 0 60px 0;
  }
  
  .filter-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }
  
  .filter-title {
    font-size: 24px;
  }
  
  .clear-filters {
    align-self: stretch;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .hero-content {
    padding: 0 16px;
  }
  
  .hero-title {
    font-size: 28px;
  }
  
  .hero-subtitle {
    font-size: 18px;
  }
  
  .hero-stats {
    flex-direction: column;
    gap: 20px;
  }
  
  .services-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .section-title {
    font-size: 24px;
  }
  
  .filter-title {
    font-size: 20px;
  }
  
  .container {
    padding: 0 16px;
  }
}

/* Animation for page load */
.services-page {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scroll animations */
.section-header {
  animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading states */
.services-grid.loading {
  opacity: 0.7;
}

.services-grid.loading .service-card {
  pointer-events: none;
}
