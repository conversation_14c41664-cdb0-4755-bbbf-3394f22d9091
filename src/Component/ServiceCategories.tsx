import React from 'react';
import { ServiceCategory } from '../data/servicesData';
import OptimizedImage from './OptimizedImage';
import '../Style/servicecategories.css';

interface ServiceCategoriesProps {
  categories: ServiceCategory[];
  onCategoryClick?: (category: ServiceCategory) => void;
  title?: string;
  showDescription?: boolean;
  variant?: 'grid' | 'horizontal';
}

const ServiceCategories: React.FC<ServiceCategoriesProps> = ({
  categories,
  onCategoryClick,
  title = "Our Services",
  showDescription = true,
  variant = 'grid'
}) => {
  const handleCategoryClick = (category: ServiceCategory) => {
    onCategoryClick?.(category);
  };

  return (
    <section className="service-categories-section">
      <div className="container">
        <div className="section-header">
          <h2 className="section-title">{title}</h2>
          {showDescription && (
            <p className="section-description">
              Professional services at your doorstep. Choose from our wide range of trusted services.
            </p>
          )}
        </div>

        <div className={`categories-container ${variant}`}>
          {categories.map((category) => (
            <div
              key={category.id}
              className="category-card"
              onClick={() => handleCategoryClick(category)}
            >
              <div className="category-image-container">
                <OptimizedImage
                  src={category.image}
                  alt={category.name}
                  className="category-image"
                  loading="lazy"
                />
                <div className="category-overlay">
                  <span className="category-icon">{category.icon}</span>
                </div>
              </div>

              <div className="category-content">
                <h3 className="category-name">{category.name}</h3>
                <p className="category-description">{category.description}</p>
                
                <div className="category-stats">
                  <span className="service-count">
                    {category.subcategories.reduce((total, sub) => total + sub.services.length, 0)} services
                  </span>
                  <span className="subcategory-count">
                    {category.subcategories.length} categories
                  </span>
                </div>

                <div className="category-action">
                  <span className="explore-text">Explore Services</span>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="m9 18 6-6-6-6"/>
                  </svg>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServiceCategories;
