import React from 'react';
import { Service } from '../data/servicesData';
import OptimizedImage from './OptimizedImage';
import '../Style/servicecard.css';

interface ServiceCardProps {
  service: Service;
  onClick?: (service: Service) => void;
  variant?: 'default' | 'compact' | 'featured';
}

const ServiceCard: React.FC<ServiceCardProps> = ({
  service,
  onClick,
  variant = 'default'
}) => {


  const formatReviewCount = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };



  const handleClick = () => {
    onClick?.(service);
  };

  return (
    <div 
      className={`service-card ${variant} ${onClick ? 'clickable' : ''}`}
      onClick={handleClick}
    >
      <div className="service-image-container">
        <OptimizedImage
          src={service.image}
          alt={service.name}
          className="service-image"
          loading="lazy"
        />
        
        {/* Badges */}
        <div className="service-badges">
          {service.isPopular && (
            <span className="badge popular">Popular</span>
          )}
          {service.isNew && (
            <span className="badge new">New</span>
          )}
        </div>
      </div>

      <div className="service-content">
        <div className="service-header">
          <h3 className="service-name">{service.name}</h3>
          <div className="service-rating">
            <span className="rating-star">⭐</span>
            <span className="rating-value">{service.rating}</span>
            <span className="rating-count">({formatReviewCount(service.reviewCount)})</span>
          </div>
        </div>

        <p className="service-description">{service.description}</p>

        <div className="service-meta">
          <div className="service-duration">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12,6 12,12 16,14"></polyline>
            </svg>
            <span>{service.duration}</span>
          </div>
        </div>

        <div className="service-action">
          <button className="contact-button">
            Get Quote
          </button>
        </div>

        {/* Tags */}
        {variant === 'featured' && service.tags.length > 0 && (
          <div className="service-tags">
            {service.tags.slice(0, 3).map((tag, index) => (
              <span key={index} className="service-tag">
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ServiceCard;
