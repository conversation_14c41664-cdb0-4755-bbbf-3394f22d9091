import { Link } from 'react-scroll';
import "../Style/ScrollDots.css"

const ScrollDots = () => {
  return (
    <>

 <div className="scroll-dots-Contain">
      <Link
        className="scroll-dot"
        activeClass="active"
        to="content_1"
        spy={true}
        smooth={true}
        duration={500}
      >  
        <span className="dot"></span>
      </Link>
      <Link
        className="scroll-dot"
        activeClass="active"
        to="content_2"
        spy={true}
        smooth={true}
        duration={500}
      >
        <span className="dot"></span>
      </Link>
      <Link
        className="scroll-dot"
        activeClass="active"
        to="content_3"
        spy={true}
        smooth={true}
        duration={500}
      >
       
      <span className="dot"> </span> 
      </Link>
      <Link
        className="scroll-dot"
        activeClass="active"
        to="content_4"
        spy={true}
        smooth={true}
        duration={500}
      >
         
        <span className="dot"></span>
      </Link>
      
      
    </div>
    </>
  )
}

export default ScrollDots
