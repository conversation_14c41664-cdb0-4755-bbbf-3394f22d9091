import React, { useState } from 'react';
import OptimizedImage from './OptimizedImage';
import '../Style/portfoliogallery.css';

interface PortfolioItem {
  id: string;
  title: string;
  category: string;
  image: string;
  description?: string;
}

interface PortfolioGalleryProps {
  title?: string;
  items: PortfolioItem[];
  categories?: string[];
  showFilter?: boolean;
}

const PortfolioGallery: React.FC<PortfolioGalleryProps> = ({
  title = "Our Portfolio",
  items,
  categories = [],
  showFilter = true
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedImage, setSelectedImage] = useState<PortfolioItem | null>(null);

  // Extract categories from items if not provided
  const allCategories = categories.length > 0 
    ? categories 
    : ['all', ...Array.from(new Set(items.map(item => item.category)))];

  const filteredItems = selectedCategory === 'all' 
    ? items 
    : items.filter(item => item.category === selectedCategory);

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
  };

  const handleImageClick = (item: PortfolioItem) => {
    setSelectedImage(item);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  const formatCategoryName = (category: string) => {
    if (category === 'all') return 'All Projects';
    return category.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  return (
    <section className="portfolio-gallery">
      <div className="container">
        <div className="portfolio-header">
          <h2 className="portfolio-title">{title}</h2>
          <p className="portfolio-subtitle">
            Explore our stunning interior design projects and get inspired for your own space
          </p>
        </div>

        {showFilter && (
          <div className="portfolio-filters">
            {allCategories.map((category) => (
              <button
                key={category}
                className={`filter-btn ${selectedCategory === category ? 'active' : ''}`}
                onClick={() => handleCategoryChange(category)}
              >
                {formatCategoryName(category)}
              </button>
            ))}
          </div>
        )}

        <div className="portfolio-grid">
          {filteredItems.map((item) => (
            <div
              key={item.id}
              className="portfolio-item"
              onClick={() => handleImageClick(item)}
            >
              <div className="portfolio-image-container">
                <OptimizedImage
                  src={item.image}
                  alt={item.title}
                  className="portfolio-image"
                  loading="lazy"
                />
                <div className="portfolio-overlay">
                  <div className="portfolio-content">
                    <h3 className="portfolio-item-title">{item.title}</h3>
                    <p className="portfolio-category">{formatCategoryName(item.category)}</p>
                    <div className="view-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredItems.length === 0 && (
          <div className="no-items">
            <p>No projects found in this category.</p>
          </div>
        )}
      </div>

      {/* Modal for enlarged image view */}
      {selectedImage && (
        <div className="portfolio-modal" onClick={closeModal}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <button className="modal-close" onClick={closeModal}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
            <div className="modal-image-container">
              <OptimizedImage
                src={selectedImage.image}
                alt={selectedImage.title}
                className="modal-image"
                loading="eager"
              />
            </div>
            <div className="modal-info">
              <h3 className="modal-title">{selectedImage.title}</h3>
              <p className="modal-category">{formatCategoryName(selectedImage.category)}</p>
              {selectedImage.description && (
                <p className="modal-description">{selectedImage.description}</p>
              )}
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default PortfolioGallery;
