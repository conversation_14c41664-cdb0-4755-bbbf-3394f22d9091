 import "../Style/PopUp.css"
  
  interface PopUp{
    closePopUp:()=>void
  }


const PopUpForm = ({closePopUp}:PopUp) => {
  return (
    <>

    <div className="popup-overlay">
          <div className="popup-content">
           
           <  form >
           <button onClick={closePopUp} className="close-button">
              &times;
            </button>
              <div className="form-group">
                <label>Name:</label>
                <input type="text" name="name" required />
              </div>
              <div className="form-group">
                <label>Email:</label>
                <input type="email" name="email" required />
              </div>
              <div className="form-group">
                <label>Phone Number:</label>
                <input type="tel" name="number" required />
              </div>
              <button type="submit">Submit</button>
            </form>
            </div></div>
   
    </>
  )
}

export default PopUpForm
