import {useState}from 'react'
import "../Style/Form.css"

const Form = ()=>{
    const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    message: ''
});

const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    console.log(e.target);
    setFormData({ ...formData, [name]: value });
};

const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    console.log('Form Data Submitted:', formData);}
    
  return (
   <>
   <div className="form-container">
            <h2>Contact Us</h2>
            <form onSubmit={handleSubmit}>
                <label htmlFor="full-name">Full Name:</label><br />
                <input
                    type="text"
                    id="full-name"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleChange}
                    required
                /><br /><br />

                <label htmlFor="email">Email Address:</label><br />
                <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                /><br /><br />

                <label htmlFor="phone">Phone Number:</label><br />
                <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    pattern="[0-9]{10}"
                    onChange={handleChange}
                    required
                /><br /><br />

                <label htmlFor="message">Enter your Message:</label><br />
                <textarea
                    id="message"
                    name="message"
                    rows={4}
                    cols={50}
                    value={formData.message}
                    onChange={handleChange}
                    required
                /><br /><br />

                <input type="submit" value="Submit" />
            </form>
        </div>
   </>
  )
}

export default Form
