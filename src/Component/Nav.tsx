import {useState,useEffect} from 'react'
import { Link } from 'react-router-dom';
import "../Style/navbar.css";
import logo from "../images/logo1.jpg";

const Nav = () => {
   
   const[sticky , setSticky]=useState(false);
   
   useEffect(()=>{
     
      window.addEventListener('scroll',()=>{
     (window.scrollY>50 &&window.scrollY<600)?setSticky(true):setSticky(false);
    })
   },[])
  
   return (
   <>
   <nav className={`container ${sticky===true?"dark-nav ":'fixed-nav'}`} >
   
     <img src={logo} alt="logo"/>
    
       <ul>
             <li><Link to="/home">Home</Link></li>
             <li><Link to="/services">Services</Link></li>
             <li><Link to="/portfolio">Portfolio</Link></li>
             <li><Link to="/about">About US</Link></li>
             <li><Link to="/projects">Projects</Link></li>
             <li><Link to="/careers">Careers</Link></li>
             <li><Link to="/gallery">Gallery</Link></li>
             <li><Link to="/contact">Contact</Link></li>

        </ul>
       
    </nav>

   </>
     )
  
   }

export default Nav
