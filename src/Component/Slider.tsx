import {useState ,useEffect} from 'react'
import OptimizedImage from './OptimizedImage';
import img1 from "../images/gallery/image_1.jpg";
import img2 from "../images/gallery/image_2.jpg";
import img3 from "../images/gallery/image_3.jpg";
import img4 from "../images/gallery/image_4.jpg";
import img5 from "../images/gallery/image_5.jpeg";
import img6 from "../images/gallery/image_6.jpg";
import img7 from "../images/gallery/image_7.jpeg";
import img8 from "../images/gallery/image_8.jpeg";
import img9 from "../images/gallery/image_9.jpg";
import img10 from "../images/gallery/image_10.jpeg";
import "../Style/Slider.css"
import "../Style/OptimizedImage.css"
interface Slider {
    autoPlay?: boolean;
    autoPlayTime?: number;
    images: string[];
  }
  

const Slider = ({ autoPlay = true, autoPlayTime = 2000 }) => {
    
    const images = [
        img1,
         img2 ,
         img3 , 
         img4 , 
         img5, 
         img6, 
         img7 ,
         img8, 
         img9 ,
         img10,
         img7 ,
         img8, 
         img9 ,
         img10,
         
     ];
      
     const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (autoPlay) {
      const interval = setInterval(() => {
        nextSlide();
      }, autoPlayTime);
      return () => clearInterval(interval);
    }
  }, [currentIndex, autoPlay, autoPlayTime]);

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);
  };

  const prevSlide = () => {
    setCurrentIndex(
      (prevIndex) => (prevIndex === 0 ? images.length - 1 : prevIndex - 1)
    );
  };

  const goToSlide = (index:number) => {
    setCurrentIndex(index);
  };
  return (
   <>

  <div className="carousel-container">
  <h1>Our Projects</h1>
      <button className="carousel-button prev" onClick={prevSlide}>
        &#8249;
      </button>
      <div className="carousel">
        <div
          className="carousel-track"
          style={{ transform: `translateX(-${currentIndex * 100}%)` }}
        >
          {images.map((image, index) => (
            <div className="carousel-slide" key={index}>
              <OptimizedImage
                src={image}
                alt={`Home Office Renovation Project ${index + 1}`}
                loading="eager"
              />
            </div>
          ))}
        </div>
      </div>
      <button className="carousel-button next" onClick={nextSlide}>
        &#8250;
      </button>
      <div className="carousel-indicators">
        {images.map((_, index) => (
          <button
            key={index}
            className={`indicator ${
              index === currentIndex ? 'active' : ''
            }`}
            onClick={() => goToSlide(index)}
          />
        ))}
      </div>
    </div>
      
    </>
)
}

export default Slider
