import {useEffect,useState,useRef} from 'react';
import { Element, Events, scrollSpy,scroller } from 'react-scroll'
import { Link } from 'react-router-dom';
import "../Style/homepage.css"
import ScrollDots from "./ScrollDots";
import SearchBar from "./SearchBar";
import ServiceCategories from "./ServiceCategories";
import PortfolioGallery from "./PortfolioGallery";
import { serviceCategories, popularServices } from '../data/servicesData';
import ServiceCard from './ServiceCard';

const HomePage = () => {


  const [currentSection, setCurrentSection] = useState(0);
  const sections = ['content_1', 'content_2', 'content_3', 'content_4',];
  const section = useRef<HTMLDivElement[]>([]); 
  const [current_Section, setCurrent_Section] = useState(0);
  const isThrottled = useRef(false);
  
  const scrollToSection = (index: number) => {
    if (section.current[index]) {
      section.current[index].scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleScroll = (event: WheelEvent) => {

    if (isThrottled.current) return; 
    isThrottled.current = true;

    setTimeout(() => {
      isThrottled.current = false; 
    }, 1000)
    if (event.deltaY > 0 && current_Section < section.current.length - 1) {
    
      setCurrent_Section(current_Section + 1);
    } else if (event.deltaY < 0 && current_Section > 0) {
      
      setCurrent_Section(current_Section - 1);
    }
  };

  useEffect(() => {
    scrollToSection(current_Section);
  },[current_Section]);

  useEffect(() => {
    const onWheel = (e: WheelEvent) => handleScroll(e);
    window.addEventListener('wheel', onWheel);

    return () => window.removeEventListener('wheel', onWheel);
  }, [current_Section]);



  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'ArrowDown') {
      
      if (currentSection < sections.length - 1) {
        setCurrentSection((prev) => prev + 1);
        scroller.scrollTo(sections[currentSection + 1], {
          duration: 800,
          delay: 200,
          smooth: 'easeInOutQuart',
        });
      }
    }

    if (event.key === 'ArrowUp') {
     
      if (currentSection > 0) {
        setCurrentSection((prev) => prev - 1);
        scroller.scrollTo(sections[currentSection - 1], {
          duration: 800,
          delay: 200,
          smooth: 'easeInOutQuart',
        });
      }
    }
  }

  

  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
  

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentSection]);

  

  useEffect(() => {
    
    document.body.style.overflowY = 'hidden';
    
    return () => {
      
      document.body.style.overflowY = 'unset';
    };
  }, []);
        
    useEffect(() => {
      Events.scrollEvent.register('begin', function () {
        console.log('begin', arguments);
      });
  
      Events.scrollEvent.register('end', function () {
        console.log('end', arguments);
      });
  
      scrollSpy.update();
  
      return () => {
        Events.scrollEvent.remove('begin');
        Events.scrollEvent.remove('end');
      };
    }, []);
    
   return (
    
    <>
    <ScrollDots/>
    <Element name="content_1"  >
    <div className="background1 container"  ref={(el: HTMLDivElement | null) => (section.current[0] = el!)}>
     <div className="header-content">
       <h1>Home services at your doorstep</h1>
       <p className="hero-subtitle">What are you looking for?</p>
       <div className="hero-search-container">
         <SearchBar placeholder="Search for services..." />
       </div>
       <Link to="/services" className="explore-services-btn">
         Explore All Services
       </Link>
     </div>
    </div>
    </Element>

    <Element name="content_2">
    <div className="background2 container"  ref={(el: HTMLDivElement | null) => (section.current[1] = el!)}>
      <div className="services-preview">
        <ServiceCategories
          categories={serviceCategories.slice(0, 3)}
          title="Our Popular Services"
          variant="horizontal"
        />
      </div>
    </div>
    </Element>
    <Element name="content_3">
    <div className="background3 container"  ref={(el: HTMLDivElement | null) => (section.current[2] = el!)}>
      <div className="popular-services-preview">
        <h2 className="section-title">Most Booked Services</h2>
        <div className="services-grid-preview">
          {popularServices.slice(0, 4).map((service) => (
            <ServiceCard
              key={service.id}
              service={service}
              variant="compact"
            />
          ))}
        </div>
        <Link to="/services" className="view-all-services">
          View All Services →
        </Link>
      </div>
    </div>
    </Element>
    <Element name="content_4">
    <div className="background4 container"  ref={(el: HTMLDivElement | null) => (section.current[3] = el!)}>
      <div className="portfolio-preview">
        <PortfolioGallery
          title="Our Recent Projects"
          items={[
            {
              id: '1',
              title: 'Modern Living Room',
              category: 'living-room',
              image: '/images/gallery/image_1.jpg'
            },
            {
              id: '2',
              title: 'Luxury Kitchen',
              category: 'kitchen',
              image: '/images/gallery/image_2.jpg'
            },
            {
              id: '3',
              title: 'Master Bedroom',
              category: 'bedroom',
              image: '/images/gallery/image_3.jpg'
            },
            {
              id: '4',
              title: 'Walk-in Wardrobe',
              category: 'wardrobe',
              image: '/images/gallery/image_4.jpg'
            }
          ]}
          showFilter={false}
        />
        <div className="portfolio-cta-home">
          <Link to="/portfolio" className="view-portfolio-btn">
            View Complete Portfolio →
          </Link>
        </div>
      </div>
    </div>
    </Element>
    
   
  </>
   )
 }
      
   

export default HomePage
