*{
   padding-top: 0px;
  margin: 0;
  box-sizing:border-box;
}
body{
    background-color:whitesmoke;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
a{
    text-decoration: none;
    color:inherit;
    line-height:1;
    cursor:pointer;
    font-size: 20px;
}
.container{
  padding-left:5%;
  padding-right:5%;
}

/* Global Image Optimization for Crystal Clear Images */
img {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  image-rendering: pixelated;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  max-width: 100%;
  height: auto;
}

/* High DPI Display Support for Retina and 4K screens */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Smooth image loading transitions */
img {
  transition: opacity 0.3s ease-in-out;
}

/* Prevent image dragging for better UX */
img {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}