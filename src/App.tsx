import { BrowserRouter,Routes,Route } from 'react-router-dom';
import Home from './Pages/Home';
import Careers from './Pages/Careers';
import Contacts from './Pages/Contacts';
import Projects from './Pages/Projects';
import AboutUs from './Pages/AboutUs';
import Gallery from './Pages/Gallery';
import Services from './Pages/Services';
import Portfolio from './Pages/Portfolio';

 function App() {

  return (
  <>
     <BrowserRouter>

       <Routes>

       <Route path="" element= {<Home/>}></Route>
       <Route path="/home" element={<Home/>}></Route>
       <Route path="/about" element={<AboutUs/>}></Route>
       <Route path="/services" element={<Services/>}></Route>
       <Route path="/portfolio" element={<Portfolio/>}></Route>
       <Route path="/careers" element={<Careers/>}></Route>
       <Route path="/contact" element={<Contacts/>}></Route>
       <Route path="/projects" element={<Projects/>}></Route>
       <Route path="/gallery" element={<Gallery/>}></Route>
       
  
       </Routes>
  
     </BrowserRouter>
  
  </>
  )
}

export default App;
