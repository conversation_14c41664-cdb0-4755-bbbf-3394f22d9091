// Image optimization utilities for better image quality

export interface ImageOptimizationOptions {
  quality?: number;
  format?: 'webp' | 'jpeg' | 'png';
  width?: number;
  height?: number;
  fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
}

/**
 * Preloads an image to improve loading performance
 */
export const preloadImage = (src: string): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = src;
  });
};

/**
 * Preloads multiple images
 */
export const preloadImages = (sources: string[]): Promise<HTMLImageElement[]> => {
  return Promise.all(sources.map(preloadImage));
};

/**
 * Creates a responsive image source set for different screen densities
 */
export const createSrcSet = (baseSrc: string, sizes: number[] = [1, 2, 3]): string => {
  return sizes
    .map(size => {
      const extension = baseSrc.split('.').pop();
      const baseName = baseSrc.replace(`.${extension}`, '');
      return `${baseName}@${size}x.${extension} ${size}x`;
    })
    .join(', ');
};

/**
 * Detects if the device supports WebP format
 */
export const supportsWebP = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const webP = new Image();
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2);
    };
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
};

/**
 * Gets the optimal image format based on browser support
 */
export const getOptimalFormat = async (originalFormat: string): Promise<string> => {
  const webpSupported = await supportsWebP();
  
  if (webpSupported && originalFormat !== 'gif') {
    return 'webp';
  }
  
  return originalFormat;
};

/**
 * Calculates the optimal image dimensions based on container size and device pixel ratio
 */
export const calculateOptimalDimensions = (
  containerWidth: number,
  containerHeight: number,
  devicePixelRatio: number = window.devicePixelRatio || 1
): { width: number; height: number } => {
  return {
    width: Math.ceil(containerWidth * devicePixelRatio),
    height: Math.ceil(containerHeight * devicePixelRatio)
  };
};

/**
 * Creates a lazy loading observer for images
 */
export const createLazyLoadObserver = (
  callback: (entry: IntersectionObserverEntry) => void,
  options: IntersectionObserverInit = {}
): IntersectionObserver => {
  const defaultOptions: IntersectionObserverInit = {
    root: null,
    rootMargin: '50px',
    threshold: 0.1,
    ...options
  };

  return new IntersectionObserver((entries) => {
    entries.forEach(callback);
  }, defaultOptions);
};

/**
 * Compresses an image using canvas (client-side)
 */
export const compressImage = (
  file: File,
  options: ImageOptimizationOptions = {}
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      const { width = img.width, height = img.height, quality = 0.8 } = options;
      
      canvas.width = width;
      canvas.height = height;

      if (ctx) {
        // Enable image smoothing for better quality
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        
        ctx.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob(
          (blob) => {
            if (blob) {
              resolve(blob);
            } else {
              reject(new Error('Failed to compress image'));
            }
          },
          `image/${options.format || 'jpeg'}`,
          quality
        );
      } else {
        reject(new Error('Failed to get canvas context'));
      }
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
};

/**
 * Generates a placeholder image with specified dimensions and color
 */
export const generatePlaceholder = (
  width: number,
  height: number,
  color: string = '#f0f0f0'
): string => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  
  canvas.width = width;
  canvas.height = height;
  
  if (ctx) {
    ctx.fillStyle = color;
    ctx.fillRect(0, 0, width, height);
    
    // Add a subtle pattern
    ctx.fillStyle = '#e0e0e0';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(`${width}×${height}`, width / 2, height / 2);
  }
  
  return canvas.toDataURL();
};

/**
 * Image quality enhancement settings for different use cases
 */
export const imageQualityPresets = {
  thumbnail: {
    quality: 0.7,
    width: 300,
    height: 300,
    fit: 'cover' as const
  },
  gallery: {
    quality: 0.85,
    width: 800,
    height: 600,
    fit: 'cover' as const
  },
  hero: {
    quality: 0.9,
    width: 1920,
    height: 1080,
    fit: 'cover' as const
  },
  fullscreen: {
    quality: 0.95,
    fit: 'contain' as const
  }
};
