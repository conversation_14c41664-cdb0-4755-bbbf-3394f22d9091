import Nav from '../Component/Nav';
import Footer from '../Component/Footer';
import OptimizedImage from '../Component/OptimizedImage';
import "../Style/Gallery.css"
import "../Style/OptimizedImage.css"
import img1 from "../images/gallery/image_1.jpg";
import img2 from "../images/gallery/image_2.jpg";
import img3 from "../images/gallery/image_3.jpg";
import img4 from "../images/gallery/image_4.jpg";
import img5 from "../images/gallery/image_5.jpeg";
import img6 from "../images/gallery/image_6.jpg";
import img7 from "../images/gallery/image_7.jpeg";
import img8 from "../images/gallery/image_8.jpeg";
import img9 from "../images/gallery/image_9.jpg";
import img10 from "../images/gallery/image_10.jpeg";
import img11 from "../images/gallery/image_11.jpeg";
import img12 from "../images/gallery/image_12.jpeg";
import img13 from "../images/gallery/image_13.jpg";
import img14 from "../images/gallery/image_14.jpeg";
import img15 from "../images/gallery/image_15.jpg";
import img16 from "../images/gallery/image_16.jpg";
import img17 from "../images/gallery/image_17.jpeg";
import img18 from "../images/gallery/image_18.jpg";
import img19 from "../images/gallery/image_19.jpeg";
import img20 from "../images/gallery/image_20.jpeg";
const Gallery = () => {
  let fullImgBox :HTMLElement;
  let  fullImg: HTMLImageElement;
    const closeImage=(event:React.MouseEvent<HTMLSpanElement, MouseEvent>)=>{
      if(fullImgBox){
         fullImgBox.style.display="none";
         // Reset image source to prevent memory leaks
         if(fullImg) {
           fullImg.src = "";
         }
       }
    }
    const openImage=(event: React.MouseEvent<HTMLImageElement, MouseEvent>):void=>{
      fullImgBox=document.getElementById("fullImageBox") as HTMLElement;
      fullImg= document.getElementById("fullImage") as HTMLImageElement;

      if(fullImgBox){
        fullImgBox.style.display="flex"
        if(fullImg){
          const image=event.target as HTMLImageElement
          const imagePath=image.src;

          // Add loading state
          fullImg.style.opacity = "0";
          fullImg.onload = () => {
            fullImg.style.opacity = "1";
          };

          fullImg.src = imagePath;
        }
      }
    }
    
  return (
   <>
   <Nav/>
    <div className='gallery'>
      <div className="full-img" id="fullImageBox">
        <img src={""} alt="1" id="fullImage"/><span onClick={closeImage}>X</span>
      </div>
    <div className="image_gallery ">
      <OptimizedImage src={img1} alt="Home Office Renovation Project 1" onClick={openImage}/>
      <OptimizedImage src={img2} alt="Home Office Renovation Project 2" onClick={openImage}/>
      <OptimizedImage src={img3} alt="Home Office Renovation Project 3" onClick={openImage}/>
      <OptimizedImage src={img4} alt="Home Office Renovation Project 4" onClick={openImage}/>
      <OptimizedImage src={img5} alt="Home Office Renovation Project 5" onClick={openImage}/>
      <OptimizedImage src={img6} alt="Home Office Renovation Project 6" onClick={openImage}/>
      <OptimizedImage src={img7} alt="Home Office Renovation Project 7" onClick={openImage}/>
      <OptimizedImage src={img8} alt="Home Office Renovation Project 8" onClick={openImage}/>
      <OptimizedImage src={img9} alt="Home Office Renovation Project 9" onClick={openImage} />
      <OptimizedImage src={img10} alt="Home Office Renovation Project 10" onClick={openImage}/>
      <OptimizedImage src={img11} alt="Home Office Renovation Project 11" onClick={openImage} />
      <OptimizedImage src={img12} alt="Home Office Renovation Project 12" onClick={openImage} />
      <OptimizedImage src={img13} alt="Home Office Renovation Project 13" onClick={openImage}/>
      <OptimizedImage src={img14} alt="Home Office Renovation Project 14" onClick={openImage}/>
      <OptimizedImage src={img15} alt="Home Office Renovation Project 15" onClick={openImage}/>
      <OptimizedImage src={img16} alt="Home Office Renovation Project 16" onClick={openImage}/>
      <OptimizedImage src={img17} alt="Home Office Renovation Project 17" onClick={openImage}/>
      <OptimizedImage src={img18} alt="Home Office Renovation Project 18" onClick={openImage}/>
      <OptimizedImage src={img19} alt="Home Office Renovation Project 19" onClick={openImage}/>
      <OptimizedImage src={img20} alt="Home Office Renovation Project 20" onClick={openImage}/>
      <OptimizedImage src={img6} alt="Home Office Renovation Project 21" onClick={openImage}/>
      <OptimizedImage src={img15} alt="Home Office Renovation Project 22" onClick={openImage}/>
      <OptimizedImage src={img9} alt="Home Office Renovation Project 23" onClick={openImage}/>
      <OptimizedImage src={img10} alt="Home Office Renovation Project 24" onClick={openImage}/>
      <OptimizedImage src={img11} alt="Home Office Renovation Project 25" onClick={openImage}/>

     

    </div>


    </div>
    
   <Footer/>
   </>
  )
}
export default Gallery
