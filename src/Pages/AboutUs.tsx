import { useState } from 'react';
import Nav from '../Component/Nav';
import Slider from '../Component/Slider';
import Footer from '../Component/Footer';
import "../Style/AboutUs.css"
import icon1 from "../images/icon1.png";
import icon2 from "../images/icon2.png"
import service1 from "../images/service1.jpeg";
import service2 from "../images/service2.jpeg"
import service3 from "../images/service3.jpg";
import service4 from "../images/service4.jpg";
import PopUpForm from '../Component/PopUpForm';


const AboutUs = () => {

  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const openPopup = () => {
    setIsPopupOpen(true);
  };

   const closePopup = ():void => {
    setIsPopupOpen(false);
  };

  return (
   <>
   <Nav/>
   <div className="aboutus">
    <div className="about_content1">

    <h1>Who we are</h1>
     <p><PERSON><PERSON><PERSON> (formerly known as 'The Ambience') was founded in 1990. The company specializes in providing furniture solutions across a variety of sectors, including corporate and housing projects.</p>

    <p> Braham Dev’s team consists of highly skilled professionals, including architects, interior designers, project engineers, production engineers, and specialists from departments such as purchasing and surveying, alongside over 100 trained technicians. We excel in time, material, and people management, ensuring seamless execution of every project.</p>

     <p>Our workshop, located in Delhi NCR, focuses on producing custom-made furniture, streamlining the execution process on-site. We are experts in designing and manufacturing office and home furniture, utilizing advanced machinery and modern technology in our state-of-the-art manufacturing facilities. By efficiently managing resources, we are able to minimize waste, which enables us to offer highly competitive pricing in the market.</p>

     <p>Some of our notable projects, ranging from government tenders to furniture for offices, homes, and schools, are a testament to our effective resource and time management. Our approach has helped us maintain strong, long-term relationships with clients well beyond the completion of their projects.</p>

    </div>
   {isPopupOpen && 
        <PopUpForm closePopUp={ closePopup }/>}
    <div className="services_looking_for">
        
         <div className="services_info">
           <h4>What are you looking for?</h4>
           <div className="rows">
            
             <div className="columns" onClick={openPopup}>
                 
                 <img src={icon1} alt="i1"  ></img>
                 <p>Wall Paintings</p>
           
           </div>
           
             <div className="columns" onClick={openPopup} >
           
                 <img src={icon2} alt="i2"></img>
                 <p>Electrial Supply</p>
         
          </div>
         
              <div className="columns" onClick={openPopup}>
               
                  <img src={icon1} alt="3"></img>
                  <p>Floor Repairing</p>
                 
          </div>
           
            </div>
        
            
            <div className="rows">
              
               
              <div className="columns" onClick={openPopup}>
                 <img src={icon1}></img>
                  <p>Construction Consultancy</p>
               
               </div>
               <div className="columns" onClick={openPopup}>
                  <img src={icon2}></img>
                   <p>House Renovation</p>
             
             </div>
          
             <div className="columns" onClick={openPopup} >
                
                <img src={icon1}></img>
                  <p>Plumbing</p>
               
                 </div>
          
          </div>
      
       </div>
       <div className="services_images">
         
         <img src={service1}/>
         <img src={service3}/>
         <img src={service2}/>
         <img src={service4}/>

          </div>
       </div>
   </div>
   <div className="services">
         
         <h1>Our Services</h1>
      
       <div className="row">
        
           <div className="service">
                <h2>Construction Consultant</h2>
                <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Hic, libero? Praesentium odio aspernatur culpa repudiandae autem dolore obcaecati cupiditate iste!</p>
          
          </div>
           
          <div className="service">
              
              <h2>House Renovation</h2>
              <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Hic, libero? Praesentium odio aspernatur culpa repudiandae autem dolore obcaecati cupiditate iste!</p>
         
         </div>
        
          <div className="service">
               <h2>Architecture and Building</h2>
              <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Hic, libero? Praesentium odio aspernatur culpa repudiandae autem dolore obcaecati cupiditate iste!</p>
        
         </div>
        
        </div>
       
       <div className="row">

             <div className="service">
              
                <h2>Wall Paintings</h2>
              <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Hic, libero? Praesentium odio aspernatur culpa repudiandae autem dolore obcaecati cupiditate iste!</p>
        
           </div>

           <div className="service">
              
               <h2>Electrical Supply</h2>
               <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Hic, libero? Praesentium odio aspernatur culpa repudiandae autem dolore obcaecati cupiditate iste!</p>
        
           </div>
            <div className="service">
          
                <h2>Plumbing</h2>
                <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Hic, libero? Praesentium odio aspernatur culpa repudiandae autem dolore obcaecati cupiditate iste!</p>
          </div>
        
        </div>
</div>
   
       <Slider/>
  
   <Footer/>
   </>
  )
}

export default AboutUs
