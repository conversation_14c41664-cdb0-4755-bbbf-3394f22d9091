import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Nav from '../Component/Nav';
import Footer from '../Component/Footer';
import SearchBar from '../Component/SearchBar';
import ServiceCategories from '../Component/ServiceCategories';
import ServiceCard from '../Component/ServiceCard';
import { serviceCategories, popularServices, newServices, Service, ServiceCategory } from '../data/servicesData';
import '../Style/services.css';

const Services: React.FC = () => {
  const navigate = useNavigate();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [filteredServices, setFilteredServices] = useState<Service[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    if (selectedCategory) {
      const category = serviceCategories.find(cat => cat.id === selectedCategory);
      if (category) {
        const services = category.subcategories.flatMap(sub => sub.services);
        setFilteredServices(services);
      }
    } else {
      setFilteredServices([]);
    }
  }, [selectedCategory]);

  const handleCategoryClick = (category: ServiceCategory) => {
    setSelectedCategory(category.id);
    setSearchQuery('');
  };

  const handleServiceSelect = (service: Service) => {
    // Navigate to service detail page
    navigate(`/service/${service.id}`);
  };

  const handleSearchService = (service: Service) => {
    setSearchQuery(service.name);
    setSelectedCategory(null);
    setFilteredServices([service]);
  };

  const clearFilters = () => {
    setSelectedCategory(null);
    setSearchQuery('');
    setFilteredServices([]);
  };

  return (
    <>
      <Nav />
      <div className="services-page">
        {/* Hero Section */}
        <section className="services-hero">
          <div className="hero-content">
            <h1 className="hero-title">
              Home services at your doorstep
            </h1>
            <p className="hero-subtitle">
              What are you looking for?
            </p>
            <div className="hero-search">
              <SearchBar 
                onServiceSelect={handleSearchService}
                placeholder="Search for services..."
              />
            </div>
            <div className="hero-stats">
              <div className="stat-item">
                <span className="stat-number">4.8</span>
                <span className="stat-label">Service Rating*</span>
              </div>
              <div className="stat-item">
                <span className="stat-number">12M+</span>
                <span className="stat-label">Customers Globally*</span>
              </div>
            </div>
          </div>
        </section>

        {/* Service Categories */}
        {!selectedCategory && !searchQuery && (
          <ServiceCategories 
            categories={serviceCategories}
            onCategoryClick={handleCategoryClick}
            title="What are you looking for?"
          />
        )}

        {/* Popular Services */}
        {!selectedCategory && !searchQuery && (
          <section className="popular-services">
            <div className="container">
              <div className="section-header">
                <h2 className="section-title">Most booked services</h2>
              </div>
              <div className="services-grid">
                {popularServices.slice(0, 6).map((service) => (
                  <ServiceCard
                    key={service.id}
                    service={service}
                    onClick={handleServiceSelect}
                    variant="compact"
                  />
                ))}
              </div>
            </div>
          </section>
        )}

        {/* New Services */}
        {!selectedCategory && !searchQuery && newServices.length > 0 && (
          <section className="new-services">
            <div className="container">
              <div className="section-header">
                <h2 className="section-title">New and noteworthy</h2>
              </div>
              <div className="services-grid">
                {newServices.map((service) => (
                  <ServiceCard
                    key={service.id}
                    service={service}
                    onClick={handleServiceSelect}
                    variant="featured"
                  />
                ))}
              </div>
            </div>
          </section>
        )}

        {/* Filtered Services */}
        {(selectedCategory || searchQuery) && (
          <section className="filtered-services">
            <div className="container">
              <div className="filter-header">
                <div className="filter-info">
                  <h2 className="filter-title">
                    {selectedCategory 
                      ? serviceCategories.find(cat => cat.id === selectedCategory)?.name
                      : `Search results for "${searchQuery}"`
                    }
                  </h2>
                  <p className="filter-count">
                    {filteredServices.length} service{filteredServices.length !== 1 ? 's' : ''} found
                  </p>
                </div>
                <button className="clear-filters" onClick={clearFilters}>
                  Clear filters
                </button>
              </div>
              
              {filteredServices.length > 0 ? (
                <div className="services-grid">
                  {filteredServices.map((service) => (
                    <ServiceCard
                      key={service.id}
                      service={service}
                      onClick={handleServiceSelect}
                    />
                  ))}
                </div>
              ) : (
                <div className="no-services">
                  <p>No services found. Try adjusting your search or browse our categories.</p>
                </div>
              )}
            </div>
          </section>
        )}
      </div>
      <Footer />
    </>
  );
};

export default Services;
