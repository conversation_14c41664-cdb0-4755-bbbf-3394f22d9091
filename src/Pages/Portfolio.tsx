import React from 'react';
import Nav from '../Component/Nav';
import Footer from '../Component/Footer';
import PortfolioGallery from '../Component/PortfolioGallery';
import '../Style/portfolio.css';

const Portfolio: React.FC = () => {
  // Portfolio items data - using existing gallery images
  const portfolioItems = [
    {
      id: '1',
      title: 'Modern Living Room Design',
      category: 'living-room',
      image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      description: 'Contemporary living room with sleek furniture and modern aesthetics'
    },
    {
      id: '2',
      title: 'Luxury Kitchen Design',
      category: 'kitchen',
      image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      description: 'Premium modular kitchen with island and high-end appliances'
    },
    {
      id: '3',
      title: 'Master Bedroom Suite',
      category: 'bedroom',
      image: 'https://images.unsplash.com/photo-1560185007-cde436f6a4d0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      description: 'Elegant master bedroom with walk-in closet and en-suite bathroom'
    },
    {
      id: '4',
      title: 'Walk-in Wardrobe',
      category: 'wardrobe',
      image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      description: 'Spacious walk-in wardrobe with organized storage solutions'
    },
    {
      id: '5',
      title: 'Contemporary Living Space',
      category: 'living-room',
      image: 'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      description: 'Open-plan living area with modern furniture and lighting'
    },
    {
      id: '6',
      title: 'L-Shaped Modular Kitchen',
      category: 'kitchen',
      image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      description: 'Efficient L-shaped kitchen design with premium fittings'
    },
    {
      id: '7',
      title: 'Kids Bedroom Design',
      category: 'bedroom',
      image: 'https://images.unsplash.com/photo-1560185007-cde436f6a4d0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      description: 'Colorful and functional bedroom design for children'
    },
    {
      id: '8',
      title: 'Custom Sliding Wardrobe',
      category: 'wardrobe',
      image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      description: 'Space-saving sliding door wardrobe with mirror finish'
    },
    {
      id: '9',
      title: 'Minimalist Living Room',
      category: 'living-room',
      image: '/images/gallery/image_9.jpg',
      description: 'Clean and simple living room design with minimal furniture'
    },
    {
      id: '10',
      title: 'Island Kitchen Design',
      category: 'kitchen',
      image: '/images/gallery/image_10.jpeg',
      description: 'Spacious kitchen with central island for cooking and dining'
    },
    {
      id: '11',
      title: 'Guest Bedroom',
      category: 'bedroom',
      image: '/images/gallery/image_11.jpeg',
      description: 'Comfortable and welcoming guest bedroom design'
    },
    {
      id: '12',
      title: 'Corner Wardrobe Solution',
      category: 'wardrobe',
      image: '/images/gallery/image_12.jpeg',
      description: 'Efficient corner wardrobe design to maximize space utilization'
    },
    {
      id: '13',
      title: 'Luxury Living Room',
      category: 'living-room',
      image: '/images/gallery/image_13.jpg',
      description: 'Premium living room design with high-end materials and finishes'
    },
    {
      id: '14',
      title: 'Parallel Kitchen Design',
      category: 'kitchen',
      image: '/images/gallery/image_14.jpeg',
      description: 'Efficient parallel kitchen design for narrow spaces'
    },
    {
      id: '15',
      title: 'Teen Bedroom Design',
      category: 'bedroom',
      image: '/images/gallery/image_15.jpg',
      description: 'Stylish and functional bedroom design for teenagers'
    },
    {
      id: '16',
      title: 'Built-in Wardrobe',
      category: 'wardrobe',
      image: '/images/gallery/image_16.jpg',
      description: 'Custom built-in wardrobe with smart storage solutions'
    },
    {
      id: '17',
      title: 'Open Living Concept',
      category: 'living-room',
      image: '/images/gallery/image_17.jpeg',
      description: 'Open-plan living room connected to dining area'
    },
    {
      id: '18',
      title: 'Compact Kitchen Design',
      category: 'kitchen',
      image: '/images/gallery/image_18.jpg',
      description: 'Smart compact kitchen design for small spaces'
    },
    {
      id: '19',
      title: 'Cozy Bedroom Retreat',
      category: 'bedroom',
      image: '/images/gallery/image_19.jpeg',
      description: 'Warm and cozy bedroom design with natural materials'
    },
    {
      id: '20',
      title: 'Dressing Room Design',
      category: 'wardrobe',
      image: '/images/gallery/image_20.jpeg',
      description: 'Luxurious dressing room with vanity and storage'
    }
  ];

  const categories = ['living-room', 'kitchen', 'bedroom', 'wardrobe'];

  return (
    <>
      <Nav />
      <div className="portfolio-page">
        {/* Hero Section */}
        <section className="portfolio-hero">
          <div className="hero-content">
            <h1 className="hero-title">Our Portfolio</h1>
            <p className="hero-subtitle">
              Discover our stunning interior design projects that transform spaces into beautiful, functional homes
            </p>
          </div>
        </section>

        {/* Portfolio Gallery */}
        <PortfolioGallery
          title="Interior Design Projects"
          items={portfolioItems}
          categories={categories}
          showFilter={true}
        />

        {/* Call to Action */}
        <section className="portfolio-cta">
          <div className="container">
            <div className="cta-content">
              <h2 className="cta-title">Ready to Transform Your Space?</h2>
              <p className="cta-description">
                Let our expert designers create a beautiful and functional interior that reflects your style and needs.
              </p>
              <div className="cta-buttons">
                <a href="/services" className="cta-btn primary">
                  Explore Services
                </a>
                <a href="/contact" className="cta-btn secondary">
                  Get Free Consultation
                </a>
              </div>
            </div>
          </div>
        </section>
      </div>
      <Footer />
    </>
  );
};

export default Portfolio;
